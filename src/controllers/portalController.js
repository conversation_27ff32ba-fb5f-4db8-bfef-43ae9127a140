// src/controllers/portalController.js
import { ResponseService } from "../services/responseService.js";
import { ValidationService } from "../services/validationService.js";
import { PortalUserService } from "../services/portalUserServiceD1.js";
import { EmailQueueService as EmailQueueServiceD1 } from "../services/emailQueueServiceD1.js";
import { TokenService } from "../services/tokenService.js";
import { UserService as UserServiceD1 } from "../services/userServiceD1.js";
import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";
import { UsageHistoryService } from "../services/usageHistoryService.js";

export class PortalController {
  constructor(env) {
    this.env = env;
    this.responseService = new ResponseService();
    this.validationService = new ValidationService();
    this.portalUserService = new PortalUserService(env);
    this.emailQueueService = new EmailQueueServiceD1(env);
    this.tokenService = new TokenService(env);
    this.userService = new UserServiceD1(env);
    this.tierService = new TierServiceD1(env);
    this.usageHistoryService = new UsageHistoryService(env);
  }

  async registerUser(request) {
    try {
      // Parse the request body
      const userData = await request.json();
      console.log("Portal registration data:", userData);

      // Validate input for portal registration
      const validation =
        this.validationService.validatePortalUserData(userData);
      if (!validation.isValid) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(validation.errors.join(", "))
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      // Check if user already exists
      const existingUser = await this.portalUserService.getPortalUserByEmail(
        userData.username
      );
      if (existingUser) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "The provided email address is already registered."
            )
          ),
          { status: 409, headers: { "Content-Type": "application/json" } }
        );
      }

      // Create user account for portal
      const newUser = await this.portalUserService.createPortalUser({
        email: userData.username,
        password: userData.password,
      });

      console.log("Debug - newUser object:", JSON.stringify(newUser, null, 2));
      console.log("Debug - activationToken:", newUser?.activationToken);

      // Validate activation token exists
      if (!newUser || !newUser.activationToken) {
        console.error("❌ Missing activation token in newUser object");
        throw new Error("Failed to generate activation token");
      }

      // Send welcome email
      try {
        await this.emailQueueService.addToQueue({
          type: "portal_welcome",
          email: userData.username,
          username: userData.username,
          password: userData.password,
          activationToken: newUser.activationToken,
        });
        console.log(`✅ Portal welcome email queued for ${userData.username}`);

        // Process the email queue immediately to ensure delivery
        try {
          console.log("🔄 Processing email queue immediately...");
          const processResults = await this.emailQueueService.processQueue(
            true
          );
          console.log("📧 Queue processing results:", processResults);
        } catch (processError) {
          console.error("❌ Error processing email queue:", processError);
          // Don't fail the request if queue processing fails
        }
      } catch (emailError) {
        console.error("❌ Failed to queue portal welcome email:", emailError);
        // We don't want to fail the whole registration for this
      }

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            {
              userId: newUser.id,
              email: newUser.email,
              createdAt: newUser.createdAt,
            },
            "Portal user registered successfully"
          )
        ),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error registering portal user:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError(
            error.message || "Internal server error during registration"
          )
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async login(request) {
    try {
      const { username, password } = await request.json();

      if (!username || !password) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "Username and password are required."
            )
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const isActive = await this.portalUserService.verifyIsActive(username);

      if (isActive === false) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "Account not activated. Please check your email and click the activation link."
            )
          ),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }

      const user = await this.portalUserService.verifyPortalUser(
        username,
        password
      );

      if (!user) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Invalid credentials.")
          ),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }

      const token = await this.tokenService.generatePortalToken(user.id);

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({ token }, "Login successful.")
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error during portal login:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError("Internal server error.")
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async forgotPassword(request) {
    try {
      const { email } = await request.json();

      if (!email) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Email is required.")
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      // Basic email format validation
      if (!this.validationService.isValidEmail(email)) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Invalid email format.")
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const user = await this.portalUserService.getPortalUserByEmail(email);

      // Respond with a generic success message to prevent email enumeration
      if (!user) {
        console.log(
          `Attempted password reset for non-existent email: ${email}`
        );
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess(
              null,
              "If an account with that email exists, a password reset link has been sent."
            )
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }

      // Generate a password reset token
      const resetToken = await this.tokenService.generatePasswordResetToken(
        user.id
      );

      // Store the password reset token in database
      const now = new Date().toISOString();
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString(); // 1 hour from now

      await this.portalUserService.db.createPasswordResetToken({
        token: resetToken,
        user_id: user.id,
        created_at: now,
        expires_at: expiresAt,
      });

      console.log(
        `✅ Password reset token stored in database for user: ${user.email}`
      );

      // Construct the password reset link
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost"; // Fallback to localhost
      const encodedEmail = Buffer.from(user.email).toString("base64");
      const resetLink = `${frontendUrl}/reset-password?token=${resetToken}&email=${encodedEmail}`;

      // Queue email to send the reset link
      await this.emailQueueService.addToQueue({
        type: "portal_password_reset",
        email: user.email,
        resetLink: resetLink,
      });

      console.log(`Password reset email queued for ${user.email}`);

      // Process the email queue immediately to ensure delivery
      try {
        console.log("🔄 Processing email queue immediately...");
        const processResults = await this.emailQueueService.processQueue(true);
        console.log("📧 Queue processing results:", processResults);
      } catch (processError) {
        console.error("❌ Error processing email queue:", processError);
        // Don't fail the request if queue processing fails
      }

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            null,
            "If an account with that email exists, a password reset link has been sent."
          )
        ),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error during forgot password:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError(
            "Internal server error during password reset request."
          )
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async resetPassword(request) {
    try {
      const { token, newPassword } = await request.json();

      if (!token || !newPassword) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "Token and new password are required."
            )
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const result = await this.portalUserService.resetPassword(
        token,
        newPassword
      );

      if (!result.success) {
        return new Response(
          JSON.stringify(this.responseService.formatError(result.message)),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            null,
            "Password has been reset successfully."
          )
        ),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error during password reset:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError(
            "Internal server error during password reset."
          )
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async getDashboardByEmail(request) {
    try {
      // Get email from query parameters
      const url = new URL(request.url);
      const email = url.searchParams.get("email");

      if (!email) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Email parameter is required")
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const normalizedEmail = email.toLowerCase().trim();

      // Get all data related to this email
      const dashboardData = await this.getAllDataByEmail(normalizedEmail);

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            dashboardData,
            "Dashboard data retrieved successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error getting dashboard by email:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError("Internal server error")
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async getAllDataByEmail(email) {
    try {
      const normalizedEmail = email.toLowerCase().trim();

      // Initialize result object
      const result = {
        email: normalizedEmail,
        portalUser: null,
        regularUser: null,
        domains: [],
        subscriptions: [],
        payments: [],
        tierInfo: null,
        usageHistory: [],
        dailyUsage: [],
        webhookEvents: [],
        emailQueue: [],
        apiKeys: [],
      };

      // 1. Get portal user data
      try {
        result.portalUser = await this.portalUserService.getPortalUserByEmail(
          normalizedEmail
        );
      } catch (error) {
        console.warn("Error getting portal user:", error);
      }

      // 2. Get regular user data
      try {
        result.regularUser = await this.userService.getUserByEmail(
          normalizedEmail
        );

        if (result.regularUser) {
          // Get domains for regular user
          result.domains = await this.userService.getDomains(normalizedEmail);

          // Extract API keys from domains
          result.apiKeys = result.domains.map((domain) => ({
            apiKey: domain.api_key,
            domain: domain.domain,
            status: domain.status,
            createdAt: domain.createdAt,
            activatedAt: domain.activatedAt,
          }));
        }
      } catch (error) {
        console.warn("Error getting regular user:", error);
      }

      // 3. Get tier information
      try {
        if (result.regularUser) {
          const tierData = await this.tierService.getEmailTier(normalizedEmail);
          if (tierData) {
            result.tierInfo = tierData;
          }
        }
      } catch (error) {
        console.warn("Error getting tier info:", error);
      }

      // 4. Get usage history for user
      try {
        if (result.regularUser) {
          const usageHistoryService = new (
            await import("../services/usageHistoryService.js")
          ).UsageHistoryService(this.env);
          result.usageHistory = await usageHistoryService.getUserHistory(
            result.regularUser.id,
            { limit: 50 }
          );
          result.dailyUsage = await usageHistoryService.getDailyUsage(
            result.regularUser.id
          );
        }
      } catch (error) {
        console.warn("Error getting usage history:", error);
      }

      // 5. Get subscriptions using D1 database
      try {
        const subscriptions = await this.userService.db.getSubscriptionsByEmail(
          normalizedEmail
        );
        result.subscriptions = subscriptions;
      } catch (error) {
        console.warn("Error getting subscriptions:", error);
      }

      // 6. Get payments using D1 database
      try {
        const payments = await this.userService.db.getPaymentsByEmail(
          normalizedEmail
        );
        result.payments = payments;
      } catch (error) {
        console.warn("Error getting payments:", error);
      }

      // 7. Get webhook events using D1 database
      try {
        const webhookEvents = await this.userService.db.getWebhookEventsByEmail(
          normalizedEmail
        );
        result.webhookEvents = webhookEvents;
      } catch (error) {
        console.warn("Error getting webhook events:", error);
      }

      // 8. Get email queue items using D1 database
      try {
        const emailQueueItems = await this.userService.db.getEmailQueueByEmail(
          normalizedEmail
        );
        result.emailQueue = emailQueueItems;
      } catch (error) {
        console.warn("Error getting email queue:", error);
      }

      return result;
    } catch (error) {
      console.error("Error in getAllDataByEmail:", error);
      throw error;
    }
  }

  async getDashboard(request) {
    try {
      // Check for authorization header
      const authHeader = request.headers.get("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "Authorization header with Bearer token is required"
            )
          ),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }

      const token = authHeader.split(" ")[1];
      const tokenResult = await this.tokenService.verifyPortalToken(token);

      if (!tokenResult.success) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "Invalid token or token has expired"
            )
          ),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }

      // Get portal user data
      const portalUser = await this.portalUserService.getPortalUserById(
        tokenResult.data.userId
      );
      if (!portalUser) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Portal user not found")
          ),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      // Get regular user data based on portal user's email
      const regularUser = await this.userService.getUserByEmail(
        portalUser.email
      );

      if (!regularUser) {
        // If no regular user found, return basic portal data
        const basicDashboardData = {
          user: {
            id: portalUser.id,
            email: portalUser.email,
            type: portalUser.type,
            createdAt: portalUser.createdAt,
            profile: portalUser.profile || {
              username: portalUser.email.split("@")[0],
              displayName: portalUser.email.split("@")[0],
            },
          },
          stats: {
            websites: { current: 0, max: 0 },
            credits: { current: 0, max: 0 },
            totalUsage: 0,
            currentTier: "none",
          },
          websites: [],
        };

        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess(
              basicDashboardData,
              "Dashboard data retrieved successfully"
            )
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }

      // Get user's domains
      const domains = await this.userService.getDomains(portalUser.email);

      // Get user's tier information
      const emailTier = await this.tierService.getEmailTier(portalUser.email);

      // Get usage statistics for all domains
      const websites = [];
      let totalCreditsUsed = 0;
      let totalScans = 0;

      for (const domain of domains) {
        // Get daily usage for the domain
        const dailyUsage = await this.usageHistoryService.getDailyUsage(
          domain.api_key
        );

        // Calculate usage from daily entries
        const domainCreditsUsed = dailyUsage.reduce((total, day) => {
          return (
            total + (day.images || 0) + (day.content || 0) + (day.title || 0)
          );
        }, 0);

        const domainScans = dailyUsage.reduce((total, day) => {
          return total + day.details.length;
        }, 0);

        totalCreditsUsed += domainCreditsUsed;
        totalScans += domainScans;

        websites.push({
          domain: domain.domain,
          status: domain.status || "pending",
          credits: domain.credits || 0,
          creditsUsed: domainCreditsUsed,
          lastScan: domain.lastScan || domain.activatedAt || domain.createdAt,
          apiKey: domain.api_key,
          totalScans: domainScans,
        });
      }

      // Prepare dashboard data
      const dashboardData = {
        user: {
          id: portalUser.id,
          email: portalUser.email,
          type: portalUser.type,
          createdAt: portalUser.createdAt,
          profile: portalUser.profile || {
            username: portalUser.email.split("@")[0],
            displayName: portalUser.email.split("@")[0],
          },
        },
        stats: {
          websites: {
            current: websites.length,
            max: emailTier.websiteLimit || 10,
          },
          credits: {
            current: totalCreditsUsed,
            max: emailTier.creditLimit || 100,
          },
          totalUsage: totalCreditsUsed,
          currentTier: emailTier.tier || "starter",
          posts: totalScans, // Total scans sebagai posts
        },
        websites: websites,
        tierInfo: {
          tier: emailTier.tier || "starter",
          tierName: emailTier.tierName || "Starter",
          creditLimit: emailTier.creditLimit || 100,
          websiteLimit: emailTier.websiteLimit || 10,
          price: emailTier.price || 0,
          features: emailTier.features || [
            "Basic API Access",
            "Standard Support",
          ],
        },
      };

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            dashboardData,
            "Dashboard data retrieved successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error getting portal dashboard:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError("Internal server error")
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async activateUser(request) {
    try {
      const url = new URL(request.url);
      const token = url.searchParams.get("token");

      if (!token) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Activation token is required")
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const result = await this.portalUserService.activateUser(token);

      if (!result.success) {
        return new Response(
          JSON.stringify(this.responseService.formatError(result.message)),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            { email: result.user.email },
            "Account activated successfully! You can now login."
          )
        ),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error activating user:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError(
            "Internal server error during activation"
          )
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async debugActivationToken(request) {
    try {
      const url = new URL(request.url);
      const email = url.searchParams.get("email");

      if (!email) {
        return new Response(
          JSON.stringify({ error: "Email parameter required" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const user = await this.portalUserService.getPortalUserByEmail(email);

      return new Response(
        JSON.stringify({
          found: !!user,
          activationToken: user?.activationToken || "NOT_FOUND",
          user: user
            ? {
                id: user.id,
                email: user.email,
                isActive: user.isActive,
                hasActivationToken: !!user.activationToken,
              }
            : null,
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Debug error:", error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }

  async redirectToGoogle() {
    try {
      const googleClientId = this.env.GOOGLE_CLIENT_ID;
      const redirectUri = this.env.GOOGLE_REDIRECT_URI;

      if (!googleClientId || !redirectUri) {
        console.error("Google OAuth environment variables not set");
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "Server configuration error for Google Login."
            )
          ),
          { status: 500 }
        );
      }

      const authUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth");
      authUrl.searchParams.set("client_id", googleClientId);
      authUrl.searchParams.set("redirect_uri", redirectUri);
      authUrl.searchParams.set("response_type", "code");
      authUrl.searchParams.set(
        "scope",
        "https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email"
      );
      authUrl.searchParams.set("access_type", "offline");
      authUrl.searchParams.set("prompt", "consent");

      return new Response(null, {
        status: 302,
        headers: {
          Location: authUrl.toString(),
        },
      });
    } catch (error) {
      console.error("Error redirecting to Google:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError(
            "Internal server error during Google redirect"
          )
        ),
        { status: 500 }
      );
    }
  }

  async handleGoogleCallback(request) {
    try {
      const url = new URL(request.url);
      const code = url.searchParams.get("code");

      // Echo/Log the authorization code received from Google
      console.log("🔑 Google OAuth Authorization Code received:");
      console.log(`   Code: ${code}`);
      console.log(`   Code length: ${code ? code.length : 0} characters`);
      console.log(`   Full callback URL: ${url.toString()}`);

      if (!code) {
        console.error("❌ Google OAuth code is missing from callback URL");
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Google OAuth code is missing.")
          ),
          { status: 400 }
        );
      }

      const {
        GOOGLE_CLIENT_ID,
        GOOGLE_CLIENT_SECRET,
        GOOGLE_REDIRECT_URI,
        FRONTEND_URL,
      } = this.env;

      console.log("🔄 Exchanging authorization code for access token...");

      // Exchange code for token
      const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          code,
          client_id: GOOGLE_CLIENT_ID,
          client_secret: GOOGLE_CLIENT_SECRET,
          redirect_uri: GOOGLE_REDIRECT_URI,
          grant_type: "authorization_code",
        }),
      });

      const tokenData = await tokenResponse.json();

      console.log("📊 Google token response:");
      console.log(`   Status: ${tokenResponse.status}`);
      console.log(`   Response: ${JSON.stringify(tokenData, null, 2)}`);

      if (tokenData.error) {
        console.error(
          "❌ Google token exchange error:",
          tokenData.error_description
        );
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              `Google OAuth error: ${tokenData.error_description}`
            )
          ),
          { status: 400 }
        );
      }

      const accessToken = tokenData.access_token;
      console.log("✅ Access token obtained successfully");
      console.log(
        `   Token length: ${accessToken ? accessToken.length : 0} characters`
      );

      // Get user info
      console.log("👤 Fetching user info from Google...");
      const userResponse = await fetch(
        "https://www.googleapis.com/oauth2/v2/userinfo",
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );
      const googleUser = await userResponse.json();

      console.log("📋 Google user info received:");
      console.log(`   Status: ${userResponse.status}`);
      console.log(`   User data: ${JSON.stringify(googleUser, null, 2)}`);
      console.log(`   Picture URL: ${googleUser.picture || "NO_PICTURE"}`);

      if (!googleUser.email) {
        console.error("❌ Could not retrieve email from Google user info");
        return new Response(
          JSON.stringify(
            this.responseService.formatError(
              "Could not retrieve email from Google."
            )
          ),
          { status: 400 }
        );
      }

      console.log(`✅ Google user email confirmed: ${googleUser.email}`);

      // Find or create user
      let user = await this.portalUserService.getPortalUserByEmail(
        googleUser.email
      );
      let isNewUser = false;

      if (!user) {
        console.log(`🆕 Creating new Google user: ${googleUser.email}`);
        user = await this.portalUserService.createPortalUser({
          email: googleUser.email,
          password: crypto.randomUUID(),
          googleId: googleUser.id,
          displayName: googleUser.name,
          picture: googleUser.picture, // Simpan picture dari Google
          isActive: 1,
        });
        isNewUser = true;
      } else {
        console.log(`👋 Existing user logging in: ${googleUser.email}`);
        // Update last login time and picture for existing user
        await this.portalUserService.updatePortalUser(user.id, {
          lastLogin: new Date().toISOString(),
          picture: googleUser.picture, // Update picture jika ada perubahan
        });
      }

      // Generate JWT token
      console.log("🔐 Generating JWT token for user...");
      const jwtToken = await this.tokenService.generatePortalToken(user.id);
      console.log(`✅ JWT token generated successfully`);
      console.log(
        `   Token length: ${jwtToken ? jwtToken.length : 0} characters`
      );

      // Redirect to frontend callback with token
      const frontendRedirectUrl = new URL(FRONTEND_URL || "/");
      frontendRedirectUrl.pathname = "/api/auth/callback";
      frontendRedirectUrl.searchParams.set("type", "google_oauth");
      frontendRedirectUrl.searchParams.set("token", jwtToken);

      console.log("🚀 Redirecting to frontend callback:");
      console.log(`   Redirect URL: ${frontendRedirectUrl.toString()}`);
      console.log(`   Is New User: ${isNewUser}`);
      console.log(`   User Email: ${user.email}`);
      console.log(
        `   Display Name: ${
          user.profile?.displayName || user.email.split("@")[0]
        }`
      );

      return new Response(null, {
        status: 302,
        headers: {
          Location: frontendRedirectUrl.toString(),
        },
      });
    } catch (error) {
      console.error("Error in Google callback:", error);
      return new Response(
        JSON.stringify(
          this.responseService.formatError(
            "Internal server error during Google login"
          )
        ),
        { status: 500 }
      );
    }
  }

  async testCreateGoogleUser(request) {
    try {
      const userData = await request.json();
      console.log("Test creating Google user:", userData);

      // Use the email from request or <NAME_EMAIL>
      const email = userData.email || "<EMAIL>";
      const googleId = userData.googleId || "123456789";
      const displayName = userData.displayName || "Santana Lawalata";

      // Check if user already exists
      let user = await this.portalUserService.getPortalUserByEmail(email);
      if (user) {
        return new Response(
          JSON.stringify({
            success: true,
            message: "User already exists",
            user: user,
          }),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }

      // Create new Google user
      user = await this.portalUserService.createPortalUser({
        email: email,
        password: crypto.randomUUID(), // Random password for Google users
        googleId: googleId,
        displayName: displayName,
        picture:
          userData.picture || "https://via.placeholder.com/96x96?text=User", // Default picture jika tidak ada
        isActive: 1, // Activate Google users immediately
      });

      console.log("✅ Test Google user created successfully:", user);

      return new Response(
        JSON.stringify({
          success: true,
          message: "Google user created successfully",
          user: user,
        }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("❌ Error in test create Google user:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async debugKVStore() {
    try {
      // Debug D1 database instead of KV
      const allPortalUsers = await this.portalUserService.getAllPortalUsers();
      console.log("All portal users from D1:", allPortalUsers);

      // Try to get specific user
      const user = await this.portalUserService.getPortalUserByEmail(
        "<EMAIL>"
      );
      console.log("User <NAME_EMAIL>:", user);

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            allPortalUsers: allPortalUsers,
            specificUser: user,
            message: "Check console for detailed logs - now using D1 database",
          },
        }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("❌ Error debugging D1 database:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async getUserData(request) {
    try {
      const url = new URL(request.url);
      const email =
        url.searchParams.get("email") || "<EMAIL>";

      console.log(`Getting user data for: ${email}`);

      // Get user by email
      const user = await this.portalUserService.getPortalUserByEmail(email);

      if (!user) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "User not found",
          }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      console.log(`Found user:`, user);

      return new Response(
        JSON.stringify({
          success: true,
          user: user,
        }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("❌ Error getting user data:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async deleteUser(request) {
    try {
      const url = new URL(request.url);
      const email = url.searchParams.get("email");

      if (!email) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Email parameter is required",
          }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      console.log(`🗑️ Portal user deletion requested for: ${email}`);

      // Delete the user and all associated data
      const result = await this.portalUserService.deletePortalUserByEmail(
        email
      );

      return new Response(
        JSON.stringify({
          success: true,
          message: "User deleted successfully",
          data: result,
        }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("❌ Error deleting portal user:", error);

      if (error.message.includes("not found")) {
        return new Response(
          JSON.stringify({
            success: false,
            message: error.message,
          }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      return new Response(
        JSON.stringify({
          success: false,
          message: "Internal server error",
          error: error.message,
        }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }
}
